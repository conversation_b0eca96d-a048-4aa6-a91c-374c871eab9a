#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试安全过滤器修复的脚本
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ai_report_complete_0728.core.generator import CompleteReportGenerator


async def test_safety_filter_fix():
    """测试安全过滤器修复"""
    print("🧪 开始测试安全过滤器修复...")
    
    # 创建生成器实例
    generator = CompleteReportGenerator(use_async=True, max_tokens=250000)
    
    # 测试可能触发安全过滤器的prompt
    test_prompts = [
        # 政策相关的prompt（可能触发安全过滤器）
        """
        请分析全球主要国家/地区的政策影响，包括：
        1. 美国的政策变化对全球市场的影响
        2. 中国的政策导向及其国际影响
        3. 欧盟的政策框架分析
        4. 印度的政策发展趋势
        
        请提供详细的政策影响分析报告。
        """,
        
        # 技术分析prompt（相对安全）
        """
        请分析地热发电技术的发展现状，包括：
        1. 技术原理和发展历程
        2. 主要技术路线对比
        3. 技术发展趋势
        4. 技术挑战和解决方案
        
        请提供详细的技术分析报告。
        """,
        
        # 市场分析prompt（相对安全）
        """
        请分析可再生能源市场的发展情况，包括：
        1. 市场规模和增长趋势
        2. 主要参与者分析
        3. 竞争格局概述
        4. 市场机遇和挑战
        
        请提供详细的市场分析报告。
        """
    ]
    
    results = []
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n{'='*60}")
        print(f"🧪 测试 {i}: {'政策分析' if i == 1 else '技术分析' if i == 2 else '市场分析'}")
        print(f"{'='*60}")
        
        try:
            # 测试异步统筹模型调用
            print(f"📝 测试异步统筹模型调用...")
            result = await generator.call_orchestrator_model_async(prompt)
            
            if result and result.strip():
                print(f"✅ 成功获取响应")
                print(f"📊 响应长度: {len(result)} 字符")
                print(f"📝 响应摘要: {result[:100]}...")
                
                # 检查是否是备用内容
                if "暂时无法生成内容" in result or "技术问题" in result:
                    print(f"⚠️ 返回了备用内容")
                else:
                    print(f"✅ 返回了正常内容")
                    
                results.append({
                    'test_id': i,
                    'success': True,
                    'content_length': len(result),
                    'is_fallback': "暂时无法生成内容" in result or "技术问题" in result
                })
            else:
                print(f"❌ 未获取到响应或响应为空")
                results.append({
                    'test_id': i,
                    'success': False,
                    'content_length': 0,
                    'is_fallback': True
                })
                
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
            results.append({
                'test_id': i,
                'success': False,
                'error': str(e),
                'content_length': 0,
                'is_fallback': True
            })
    
    # 输出测试总结
    print(f"\n{'='*60}")
    print(f"🧪 测试总结")
    print(f"{'='*60}")
    
    successful_tests = sum(1 for r in results if r['success'])
    fallback_tests = sum(1 for r in results if r.get('is_fallback', False))
    
    print(f"📊 总测试数: {len(test_prompts)}")
    print(f"✅ 成功测试: {successful_tests}")
    print(f"❌ 失败测试: {len(test_prompts) - successful_tests}")
    print(f"⚠️ 使用备用内容: {fallback_tests}")
    
    for result in results:
        test_name = ['政策分析', '技术分析', '市场分析'][result['test_id'] - 1]
        status = "✅ 成功" if result['success'] else "❌ 失败"
        fallback_status = " (备用内容)" if result.get('is_fallback', False) else ""
        print(f"   测试 {result['test_id']} ({test_name}): {status}{fallback_status}")
    
    # 检查修复是否有效
    if successful_tests > 0:
        print(f"\n🎉 修复验证: 安全过滤器处理机制正常工作")
        if fallback_tests < len(test_prompts):
            print(f"✅ 部分测试成功获取了正常内容，说明重试机制有效")
        else:
            print(f"⚠️ 所有测试都使用了备用内容，可能需要进一步优化prompt策略")
    else:
        print(f"\n❌ 修复验证: 仍存在问题，需要进一步调试")
    
    return results


def test_extract_content():
    """测试_extract_content方法的安全过滤器检测"""
    print(f"\n🧪 测试_extract_content方法...")
    
    generator = CompleteReportGenerator(use_async=False, max_tokens=250000)
    
    # 模拟不同类型的响应
    class MockResponse:
        def __init__(self, finish_reason=1, text_content="正常内容"):
            self.candidates = [MockCandidate(finish_reason, text_content)]
    
    class MockCandidate:
        def __init__(self, finish_reason, text_content):
            self.finish_reason = finish_reason
            self.content = MockContent(text_content)
    
    class MockContent:
        def __init__(self, text_content):
            self.parts = [MockPart(text_content)]
    
    class MockPart:
        def __init__(self, text_content):
            self.text = text_content
    
    # 测试不同的finish_reason
    test_cases = [
        (1, "正常内容", "正常完成"),
        (3, "被过滤内容", "安全过滤器阻止"),
        (4, "版权内容", "版权问题"),
        (7, "黑名单内容", "黑名单阻止"),
        (8, "禁止内容", "禁止内容")
    ]
    
    for finish_reason, content, description in test_cases:
        mock_response = MockResponse(finish_reason, content)
        extracted = generator._extract_content(mock_response)
        
        if finish_reason == 1:
            expected = content
        else:
            expected = ""
        
        status = "✅" if extracted == expected else "❌"
        print(f"   {status} finish_reason={finish_reason} ({description}): {'提取成功' if extracted == expected else '提取失败'}")


if __name__ == "__main__":
    print("🚀 开始安全过滤器修复测试...")
    
    # 测试_extract_content方法
    test_extract_content()
    
    # 测试完整的异步调用流程
    try:
        results = asyncio.run(test_safety_filter_fix())
        print(f"\n🎯 测试完成！")
    except KeyboardInterrupt:
        print(f"\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
