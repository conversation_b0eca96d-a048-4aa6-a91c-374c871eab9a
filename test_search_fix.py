#!/usr/bin/env python3
"""
测试联网搜索功能修复
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from complete_report_generator import CompleteReportGenerator

def test_search_functionality():
    """测试搜索功能"""
    print("🔧 测试联网搜索功能修复...")

    try:
        # 初始化报告生成器
        generator = CompleteReportGenerator(use_async=False)

        # 验证配置修复
        print("\n🔧 验证配置修复...")
        auto_confirm = generator.report_config.get("search_auto_confirm", False)
        print(f"   search_auto_confirm: {auto_confirm}")
        if auto_confirm:
            print("   ✅ 自动搜索确认已启用")
        else:
            print("   ❌ 自动搜索确认未启用")
            return False

        # 测试搜索功能
        search_works = generator.test_search_functionality()

        if search_works:
            print("\n✅ 搜索功能测试通过！")

            # 测试1: 工具调用搜索增强流程（模拟正式运行）
            print("\n🔍 测试1: 工具调用搜索增强流程（模拟正式运行）...")

            # 创建一个简单的测试报告
            test_content = """
# 人工智能发展报告

## 1. 概述
人工智能技术正在快速发展。

## 2. 技术趋势
机器学习和深度学习是主要方向。

## 3. 市场分析
AI市场规模持续增长。
"""

            # 保存测试报告
            test_report_path = "test_report.md"
            with open(test_report_path, 'w', encoding='utf-8') as f:
                f.write(test_content)

            # 测试工具调用搜索增强（模拟正式运行的参数）
            try:
                # 使用与正式运行相同的参数调用
                auto_confirm = generator.report_config.get("search_auto_confirm", True)
                enhanced_path = generator.enhance_report_with_tool_calling(
                    test_report_path,
                    "人工智能发展",
                    user_confirm=not auto_confirm  # 修复后的逻辑
                )

                if enhanced_path != test_report_path:
                    print(f"✅ 工具调用搜索增强成功！增强报告: {enhanced_path}")

                    # 显示增强后的内容片段
                    if os.path.exists(enhanced_path):
                        with open(enhanced_path, 'r', encoding='utf-8') as f:
                            enhanced_content = f.read()

                        print(f"   📄 增强后的报告长度: {len(enhanced_content)} 字符")
                        if len(enhanced_content) > len(test_content):
                            print("   ✅ 报告内容已被成功增强！")
                        else:
                            print("   ⚠️ 报告内容未明显增强")

                    test1_success = True
                else:
                    print("   ⚠️ 工具调用搜索增强未执行")
                    test1_success = False

            except Exception as e:
                print(f"   ❌ 工具调用搜索增强测试失败: {str(e)}")
                import traceback
                traceback.print_exc()
                test1_success = False

            # 测试2: 完整报告生成流程（模拟正式运行）
            print("\n🔍 测试2: 完整报告生成流程（模拟正式运行）...")

            try:
                # 模拟正式运行的完整流程
                print("   🚀 开始模拟完整报告生成...")

                # 设置较小的目标字数以加快测试
                original_target = generator.report_config["target_words"]
                generator.report_config["target_words"] = 1000  # 临时设置为1000字

                # 创建简单的数据源
                test_data_sources = ["人工智能技术发展现状", "AI市场趋势分析"]

                # 生成报告（这会触发搜索增强）
                final_report_path = generator.generate_report(
                    topic="人工智能发展测试",
                    data_sources=test_data_sources,
                    framework_file_path=None
                )

                # 恢复原始设置
                generator.report_config["target_words"] = original_target

                if final_report_path and os.path.exists(final_report_path):
                    print(f"   ✅ 完整流程测试成功！最终报告: {final_report_path}")

                    # 检查是否包含搜索增强的标识
                    with open(final_report_path, 'r', encoding='utf-8') as f:
                        final_content = f.read()

                    if "search_enhanced" in final_report_path or len(final_content) > 2000:
                        print("   ✅ 搜索增强在完整流程中正常执行！")
                        test2_success = True
                    else:
                        print("   ⚠️ 搜索增强可能未在完整流程中执行")
                        test2_success = False
                else:
                    print("   ❌ 完整流程测试失败")
                    test2_success = False

            except Exception as e:
                print(f"   ❌ 完整流程测试失败: {str(e)}")
                import traceback
                traceback.print_exc()
                test2_success = False

            finally:
                # 清理测试文件
                cleanup_patterns = [
                    "test_report*.md",
                    "*人工智能发展测试*.md",
                    "*人工智能发展测试*.docx"
                ]

                import glob
                for pattern in cleanup_patterns:
                    for file_path in glob.glob(pattern):
                        try:
                            os.remove(file_path)
                            print(f"   🗑️ 已清理测试文件: {file_path}")
                        except:
                            pass

            return test1_success and test2_success

        else:
            print("\n❌ 搜索功能测试失败！")
            return False

    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 联网搜索功能修复测试")
    print("=" * 60)
    
    success = test_search_functionality()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 联网搜索功能修复成功！")
        print("🎉 现在可以正常使用搜索增强功能了")
    else:
        print("❌ 联网搜索功能仍有问题")
        print("💡 请检查网络连接和API配置")
    print("=" * 60)
    
    return success

if __name__ == "__main__":
    main()
